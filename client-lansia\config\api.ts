import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://your-production-api.com/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');
      // You might want to redirect to login screen here
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const endpoints = {
  // Auth endpoints
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    profile: '/auth/profile',
    changePassword: '/auth/change-password',
    verifyToken: '/auth/verify-token',
  },
  
  // Profile endpoints
  profiles: {
    list: '/profiles',
    create: '/profiles',
    getById: (id: string) => `/profiles/${id}`,
    update: (id: string) => `/profiles/${id}`,
    delete: (id: string) => `/profiles/${id}`,
    getQR: (id: string) => `/profiles/${id}/qr`,
    scan: '/profiles/scan',
  },
  
  // Checkup endpoints
  checkups: {
    list: '/checkups',
    create: '/checkups',
    getById: (id: string) => `/checkups/${id}`,
    update: (id: string) => `/checkups/${id}`,
    delete: (id: string) => `/checkups/${id}`,
  },
  
  // Dashboard endpoints
  dashboard: {
    stats: '/dashboard/stats',
    alerts: '/dashboard/alerts',
    reports: '/dashboard/reports',
  },
  
  // Health check
  health: '/health',
};

// API helper functions
export const apiHelpers = {
  // Auth helpers
  async login(credentials: { username: string; password: string }) {
    try {
      const response = await api.post(endpoints.auth.login, credentials);
      if (response.data.success && response.data.data.token) {
        await AsyncStorage.setItem('auth_token', response.data.data.token);
        await AsyncStorage.setItem('user_data', JSON.stringify(response.data.data.user));
      }
      return response.data;
    } catch (error: any) {
      console.error('Login API error:', error);
      throw error;
    }
  },

  async logout() {
    try {
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');
    } catch (error: any) {
      console.error('Logout error:', error);
      throw error;
    }
  },

  async getCurrentUser() {
    try {
      const userData = await AsyncStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  },

  async isAuthenticated() {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      return !!token;
    } catch {
      return false;
    }
  },

  // Profile helpers
  async getProfiles(params?: { page?: number; limit?: number; search?: string }) {
    try {
      const response = await api.get(endpoints.profiles.list, { params });
      return response.data;
    } catch (error: any) {
      console.error('Get profiles API error:', error);
      throw error;
    }
  },

  async createProfile(profileData: any) {
    try {
      const response = await api.post(endpoints.profiles.create, profileData);
      return response.data;
    } catch (error: any) {
      console.error('Create profile API error:', error);
      throw error;
    }
  },

  async getProfileById(id: string) {
    try {
      const response = await api.get(endpoints.profiles.getById(id));
      return response.data;
    } catch (error: any) {
      console.error('Get profile API error:', error);
      throw error;
    }
  },

  async updateProfile(id: string, profileData: any) {
    try {
      const response = await api.put(endpoints.profiles.update(id), profileData);
      return response.data;
    } catch (error: any) {
      console.error('Update profile API error:', error);
      throw error;
    }
  },

  async deleteProfile(id: string) {
    try {
      const response = await api.delete(endpoints.profiles.delete(id));
      return response.data;
    } catch (error: any) {
      console.error('Delete profile API error:', error);
      throw error;
    }
  },

  async getProfileQR(id: string) {
    try {
      const response = await api.get(endpoints.profiles.getQR(id));
      return response.data;
    } catch (error: any) {
      console.error('Get profile QR API error:', error);
      throw error;
    }
  },

  async scanQRCode(qrData: string) {
    try {
      const response = await api.post(endpoints.profiles.scan, { qr_data: qrData });
      return response.data;
    } catch (error: any) {
      console.error('Scan QR code API error:', error);
      throw error;
    }
  },

  // Checkup helpers
  async getCheckups(params?: { page?: number; limit?: number; profile_id?: string }) {
    try {
      const response = await api.get(endpoints.checkups.list, { params });
      return response.data;
    } catch (error: any) {
      console.error('Get checkups API error:', error);
      throw error;
    }
  },

  async createCheckup(checkupData: any) {
    try {
      const response = await api.post(endpoints.checkups.create, checkupData);
      return response.data;
    } catch (error: any) {
      console.error('Create checkup API error:', error);
      throw error;
    }
  },

  async updateCheckup(id: string, checkupData: any) {
    try {
      const response = await api.put(endpoints.checkups.update(id), checkupData);
      return response.data;
    } catch (error: any) {
      console.error('Update checkup API error:', error);
      throw error;
    }
  },

  async deleteCheckup(id: string) {
    try {
      const response = await api.delete(endpoints.checkups.delete(id));
      return response.data;
    } catch (error: any) {
      console.error('Delete checkup API error:', error);
      throw error;
    }
  },

  // Dashboard helpers
  async getDashboardStats() {
    try {
      const response = await api.get(endpoints.dashboard.stats);
      return response.data;
    } catch (error: any) {
      console.error('Dashboard stats API error:', error);
      throw error;
    }
  },

  async getDashboardAlerts() {
    try {
      const response = await api.get(endpoints.dashboard.alerts);
      return response.data;
    } catch (error: any) {
      console.error('Dashboard alerts API error:', error);
      throw error;
    }
  },

  async getDashboardReports(period?: string) {
    try {
      const response = await api.get(endpoints.dashboard.reports, {
        params: period ? { period } : {}
      });
      return response.data;
    } catch (error: any) {
      console.error('Dashboard reports API error:', error);
      throw error;
    }
  },

  // Profile History and Charts
  async getProfileHistory(id: string) {
    try {
      // Use the existing profile endpoint which already includes checkup history
      const response = await api.get(endpoints.profiles.getById(id));
      return response.data;
    } catch (error: any) {
      console.error('Profile history API error:', error);
      throw error;
    }
  },

  async getProfileCharts(id: string, period: string = '3m') {
    try {
      const response = await api.get(`${endpoints.profiles.getById(id)}/charts`, {
        params: { period }
      });
      return response.data;
    } catch (error: any) {
      console.error('Profile charts API error:', error);
      throw error;
    }
  },

  // Health Analytics
  async getHealthTrends(profileId: string, params?: { period?: string; metric?: string }) {
    try {
      const response = await api.get(`/analytics/trends/${profileId}`, { params });
      return response.data;
    } catch (error: any) {
      console.error('Health trends API error:', error);
      throw error;
    }
  },

  async getHealthAlerts(profileId?: string) {
    try {
      const url = profileId ? `/analytics/alerts/${profileId}` : '/analytics/alerts';
      const response = await api.get(url);
      return response.data;
    } catch (error: any) {
      console.error('Health alerts API error:', error);
      throw error;
    }
  },

  // Reports
  async generateReport(params: {
    type: 'profile' | 'checkup' | 'summary';
    id?: string;
    period?: string;
    format?: 'pdf' | 'excel'
  }) {
    try {
      const response = await api.post('/reports/generate', params);
      return response.data;
    } catch (error: any) {
      console.error('Generate report API error:', error);
      throw error;
    }
  },

  // Health check
  async healthCheck() {
    try {
      const response = await api.get(endpoints.health);
      return response.data;
    } catch (error: any) {
      console.error('Health check API error:', error);
      throw error;
    }
  },
};

export default api;
