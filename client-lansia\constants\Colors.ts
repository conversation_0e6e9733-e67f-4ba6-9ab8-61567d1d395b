/**
 * Modern Color System for Lansia Health App
 * Based on modern design principles with accessibility in mind
 */

const tintColorLight = '#2563eb';
const tintColorDark = '#3b82f6';

export const Colors = {
  light: {
    // Primary colors
    primary: '#2563eb',
    primaryForeground: '#ffffff',
    primaryMuted: '#dbeafe',

    // Secondary colors
    secondary: '#64748b',
    secondaryForeground: '#ffffff',
    secondaryMuted: '#f1f5f9',

    // Accent colors
    accent: '#10b981',
    accentForeground: '#ffffff',
    accentMuted: '#d1fae5',

    // Background colors
    background: '#ffffff',
    backgroundSecondary: '#f8fafc',
    backgroundMuted: '#f1f5f9',

    // Surface colors
    surface: '#ffffff',
    surfaceSecondary: '#f8fafc',
    surfaceMuted: '#e2e8f0',

    // Text colors
    text: '#0f172a',
    textSecondary: '#475569',
    textMuted: '#64748b',
    textInverse: '#ffffff',

    // Border colors
    border: '#e2e8f0',
    borderMuted: '#f1f5f9',

    // Status colors
    success: '#10b981',
    successForeground: '#ffffff',
    successMuted: '#d1fae5',

    warning: '#f59e0b',
    warningForeground: '#ffffff',
    warningMuted: '#fef3c7',

    error: '#ef4444',
    errorForeground: '#ffffff',
    errorMuted: '#fecaca',

    info: '#3b82f6',
    infoForeground: '#ffffff',
    infoMuted: '#dbeafe',

    // Health-specific colors
    healthGood: '#10b981',
    healthWarning: '#f59e0b',
    healthDanger: '#ef4444',
    healthNeutral: '#64748b',

    // Chart colors
    chart: {
      primary: '#2563eb',
      secondary: '#10b981',
      tertiary: '#f59e0b',
      quaternary: '#8b5cf6',
      danger: '#ef4444',
    },

    // Legacy support
    tint: tintColorLight,
    tabIconDefault: '#64748b',
    tabIconSelected: tintColorLight,
  },
  dark: {
    // Primary colors
    primary: '#3b82f6',
    primaryForeground: '#ffffff',
    primaryMuted: '#1e3a8a',

    // Secondary colors
    secondary: '#94a3b8',
    secondaryForeground: '#0f172a',
    secondaryMuted: '#334155',

    // Accent colors
    accent: '#34d399',
    accentForeground: '#0f172a',
    accentMuted: '#064e3b',

    // Background colors
    background: '#0f172a',
    backgroundSecondary: '#1e293b',
    backgroundMuted: '#334155',

    // Surface colors
    surface: '#1e293b',
    surfaceSecondary: '#334155',
    surfaceMuted: '#475569',

    // Text colors
    text: '#f8fafc',
    textSecondary: '#cbd5e1',
    textMuted: '#94a3b8',
    textInverse: '#0f172a',

    // Border colors
    border: '#334155',
    borderMuted: '#475569',

    // Status colors
    success: '#34d399',
    successForeground: '#0f172a',
    successMuted: '#064e3b',

    warning: '#fbbf24',
    warningForeground: '#0f172a',
    warningMuted: '#451a03',

    error: '#f87171',
    errorForeground: '#0f172a',
    errorMuted: '#7f1d1d',

    info: '#60a5fa',
    infoForeground: '#0f172a',
    infoMuted: '#1e3a8a',

    // Health-specific colors
    healthGood: '#34d399',
    healthWarning: '#fbbf24',
    healthDanger: '#f87171',
    healthNeutral: '#94a3b8',

    // Chart colors
    chart: {
      primary: '#3b82f6',
      secondary: '#34d399',
      tertiary: '#fbbf24',
      quaternary: '#a78bfa',
      danger: '#f87171',
    },

    // Legacy support
    tint: tintColorDark,
    tabIconDefault: '#94a3b8',
    tabIconSelected: tintColorDark,
  },
};

// Health status color mapping
export const getHealthStatusColor = (status: 'good' | 'warning' | 'danger' | 'neutral', theme: 'light' | 'dark' = 'light') => {
  const colors = Colors[theme];
  switch (status) {
    case 'good':
      return colors.healthGood;
    case 'warning':
      return colors.healthWarning;
    case 'danger':
      return colors.healthDanger;
    default:
      return colors.healthNeutral;
  }
};

// Blood sugar level color mapping
export const getBloodSugarColor = (level: number, theme: 'light' | 'dark' = 'light') => {
  if (level < 70) return getHealthStatusColor('danger', theme);
  if (level > 200) return getHealthStatusColor('danger', theme);
  if (level > 140) return getHealthStatusColor('warning', theme);
  return getHealthStatusColor('good', theme);
};

// Blood pressure color mapping
export const getBloodPressureColor = (systolic: number, diastolic: number, theme: 'light' | 'dark' = 'light') => {
  if (systolic >= 180 || diastolic >= 110) return getHealthStatusColor('danger', theme);
  if (systolic >= 140 || diastolic >= 90) return getHealthStatusColor('warning', theme);
  if (systolic >= 120 || diastolic >= 80) return getHealthStatusColor('warning', theme);
  return getHealthStatusColor('good', theme);
};
