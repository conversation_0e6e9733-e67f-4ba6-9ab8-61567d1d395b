import React, { Component, ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { Colors } from '@/constants/Colors';
import { Typo<PERSON>, <PERSON><PERSON>, Card, CardContent } from './ui';
import { Ionicons } from '@expo/vector-icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View style={styles.container}>
          <Card variant="outlined" style={styles.errorCard}>
            <CardContent>
              <View style={styles.errorContent}>
                <Ionicons 
                  name="warning" 
                  size={48} 
                  color="#ef4444"
                  style={styles.errorIcon}
                />
                
                <Typography variant="h3" weight="semibold" align="center">
                  Terjadi Kesalahan
                </Typography>
                
                <Typography variant="body2" color="muted" align="center">
                  Aplikasi mengalami masalah yang tidak terduga. Silakan coba lagi.
                </Typography>
                
                {__DEV__ && this.state.error && (
                  <View style={styles.errorDetails}>
                    <Typography variant="caption" color="muted" align="center">
                      Error: {this.state.error.message}
                    </Typography>
                  </View>
                )}
                
                <Button
                  title="Coba Lagi"
                  onPress={this.handleRetry}
                  icon={<Ionicons name="refresh" size={20} color={Colors.light.primaryForeground} />}
                  style={styles.retryButton}
                />
              </View>
            </CardContent>
          </Card>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.background,
  },
  errorCard: {
    maxWidth: 400,
    width: '100%',
  },
  errorContent: {
    alignItems: 'center',
    gap: 16,
    padding: 20,
  },
  errorIcon: {
    marginBottom: 8,
  },
  errorDetails: {
    marginTop: 8,
    padding: 12,
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    width: '100%',
  },
  retryButton: {
    marginTop: 8,
    minWidth: 120,
  },
});
