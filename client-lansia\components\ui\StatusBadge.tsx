import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Colors, getHealthStatusColor } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Typography } from './Typography';

interface StatusBadgeProps {
  status: 'good' | 'warning' | 'danger' | 'neutral';
  label: string;
  variant?: 'filled' | 'outlined' | 'subtle';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  label,
  variant = 'filled',
  size = 'md',
  style,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const statusColor = getHealthStatusColor(status, colorScheme ?? 'light');

  const getBadgeStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'flex-start',
    };

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: { paddingHorizontal: 8, paddingVertical: 4, minHeight: 24 },
      md: { paddingHorizontal: 12, paddingVertical: 6, minHeight: 28 },
      lg: { paddingHorizontal: 16, paddingVertical: 8, minHeight: 32 },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      filled: {
        backgroundColor: statusColor,
      },
      outlined: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: statusColor,
      },
      subtle: {
        backgroundColor: `${statusColor}20`, // 20% opacity
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextColor = () => {
    switch (variant) {
      case 'filled':
        return colors.textInverse;
      case 'outlined':
      case 'subtle':
        return statusColor;
      default:
        return colors.textInverse;
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'caption' as const;
      case 'lg':
        return 'body2' as const;
      default:
        return 'body2' as const;
    }
  };

  return (
    <View style={[getBadgeStyle(), style]}>
      <Typography
        variant={getTextSize()}
        style={{ color: getTextColor() }}
        weight="medium"
      >
        {label}
      </Typography>
    </View>
  );
};

// Specialized Health Status Badges
interface HealthBadgeProps {
  value: number;
  type: 'blood_sugar' | 'blood_pressure';
  variant?: 'filled' | 'outlined' | 'subtle';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
  systolic?: number; // For blood pressure
  diastolic?: number; // For blood pressure
}

export const HealthBadge: React.FC<HealthBadgeProps> = ({
  value,
  type,
  variant = 'filled',
  size = 'md',
  style,
  systolic,
  diastolic,
}) => {

  const getStatusAndLabel = () => {
    if (type === 'blood_sugar') {
      let status: 'good' | 'warning' | 'danger' | 'neutral';
      let label: string;

      if (value < 70) {
        status = 'danger';
        label = 'Rendah';
      } else if (value > 200) {
        status = 'danger';
        label = 'Tinggi';
      } else if (value > 140) {
        status = 'warning';
        label = 'Sedang';
      } else {
        status = 'good';
        label = 'Normal';
      }

      return { status, label: `${value} mg/dL - ${label}` };
    }

    if (type === 'blood_pressure' && systolic && diastolic) {
      let status: 'good' | 'warning' | 'danger' | 'neutral';
      let label: string;

      if (systolic >= 180 || diastolic >= 110) {
        status = 'danger';
        label = 'Krisis';
      } else if (systolic >= 140 || diastolic >= 90) {
        status = 'warning';
        label = 'Tinggi';
      } else if (systolic >= 120 || diastolic >= 80) {
        status = 'warning';
        label = 'Pra-Hipertensi';
      } else {
        status = 'good';
        label = 'Normal';
      }

      return { status, label: `${systolic}/${diastolic} - ${label}` };
    }

    return { status: 'neutral' as const, label: 'N/A' };
  };

  const { status, label } = getStatusAndLabel();

  return (
    <StatusBadge
      status={status}
      label={label}
      variant={variant}
      size={size}
      style={style}
    />
  );
};

// Quick Status Indicators
interface QuickStatusProps {
  count: number;
  label: string;
  status: 'good' | 'warning' | 'danger' | 'neutral';
  style?: ViewStyle;
}

export const QuickStatus: React.FC<QuickStatusProps> = ({
  count,
  label,
  status,
  style,
}) => {
  const colorScheme = useColorScheme();
  const statusColor = getHealthStatusColor(status, colorScheme ?? 'light');

  return (
    <View style={[styles.quickStatus, style]}>
      <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
      <View style={styles.statusContent}>
        <Typography variant="h4" weight="bold">
          {count}
        </Typography>
        <Typography variant="caption" color="muted">
          {label}
        </Typography>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  quickStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  statusIndicator: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: 12,
  },
  statusContent: {
    flex: 1,
  },
});
