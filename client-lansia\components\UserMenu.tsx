import React, { useState } from 'react';
import { View, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'expo-router';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Typography, Heading3, Button } from '@/components/ui';

interface UserMenuProps {
  visible: boolean;
  onClose: () => void;
}

export function UserMenu({ visible, onClose }: UserMenuProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { user, logout } = useAuth();
  const router = useRouter();
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  const handleLogout = () => {
    setShowLogoutConfirm(true);
  };

  const confirmLogout = async () => {
    try {
      await logout();
      setShowLogoutConfirm(false);
      onClose();
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Logout error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Gagal logout. Silakan coba lagi.',
      });
    }
  };

  const cancelLogout = () => {
    setShowLogoutConfirm(false);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.overlay} 
        activeOpacity={1} 
        onPress={onClose}
      >
        <View style={[styles.menuContainer, { backgroundColor: colors.background }]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>

          <View style={styles.userInfo}>
            <View style={[styles.avatar, { backgroundColor: colors.primary }]}>
              <Ionicons name="person" size={32} color={colors.primaryForeground} />
            </View>
            <Heading3 weight="bold" style={styles.userName}>
              {user?.nama || 'Petugas'}
            </Heading3>
            <Typography variant="body2" color="muted">
              {user?.username || '<EMAIL>'}
            </Typography>
          </View>

          <View style={styles.menuItems}>
            <TouchableOpacity 
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => {
                onClose();
                // Navigate to profile settings if needed
              }}
            >
              <Ionicons name="person-outline" size={20} color={colors.text} />
              <Typography variant="body1" style={styles.menuItemText}>
                Profil Saya
              </Typography>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, { borderBottomColor: colors.border }]}
              onPress={() => {
                onClose();
                // Navigate to settings if needed
              }}
            >
              <Ionicons name="settings-outline" size={20} color={colors.text} />
              <Typography variant="body1" style={styles.menuItemText}>
                Pengaturan
              </Typography>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.menuItem, styles.logoutItem]}
              onPress={handleLogout}
            >
              <Ionicons name="log-out-outline" size={20} color="#ef4444" />
              <Typography variant="body1" style={styles.menuItemText}>
                Logout
              </Typography>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>

      {/* Logout Confirmation Modal */}
      <Modal
        visible={showLogoutConfirm}
        transparent
        animationType="fade"
        onRequestClose={cancelLogout}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={cancelLogout}
        >
          <View style={[styles.confirmDialog, { backgroundColor: colors.background }]}>
            <TouchableOpacity
              style={styles.dialogContent}
              activeOpacity={1}
            >
              <View style={styles.dialogHeader}>
                <Ionicons name="log-out-outline" size={32} color="#ef4444" />
                <Heading3 weight="bold" style={styles.dialogTitle}>
                  Konfirmasi Logout
                </Heading3>
              </View>

              <Typography variant="body1" style={styles.dialogMessage}>
                Apakah Anda yakin ingin keluar dari aplikasi?
              </Typography>

              <View style={styles.dialogButtons}>
                <Button
                  title="Batal"
                  variant="outline"
                  onPress={cancelLogout}
                  style={styles.dialogButton}
                />
                <Button
                  title="Logout"
                  variant="primary"
                  onPress={confirmLogout}
                  style={styles.dialogButton}
                />
              </View>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  menuContainer: {
    width: '100%',
    maxWidth: 320,
    borderRadius: 16,
    padding: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 24,
    paddingTop: 16,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  userName: {
    marginBottom: 4,
  },
  menuItems: {
    gap: 0,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
  logoutItem: {
    borderBottomWidth: 0,
  },
  menuItemText: {
    marginLeft: 12,
    flex: 1,
  },
  confirmDialog: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    margin: 20,
    maxWidth: 320,
    width: '100%',
  },
  dialogContent: {
    alignItems: 'center',
  },
  dialogHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  dialogTitle: {
    marginTop: 8,
    textAlign: 'center',
  },
  dialogMessage: {
    textAlign: 'center',
    marginBottom: 24,
  },
  dialogButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  dialogButton: {
    flex: 1,
  },
});
