import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Modal,
} from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading3,
} from '@/components/ui';



interface QRGeneratorProps {
  visible: boolean;
  onClose: () => void;
  profileId: string;
  profileName: string;
}

interface QRData {
  qr_code: string;
  qr_data: any;
  profile: any;
}

export const QRGenerator: React.FC<QRGeneratorProps> = ({
  visible,
  onClose,
  profileId,
  profileName,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [qrData, setQrData] = useState<QRData | null>(null);
  const [isLoading, setIsLoading] = useState(false);


  const fetchQRData = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiHelpers.getProfileQR(profileId);
      
      if (response.success && response.data) {
        setQrData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Generate QR Code',
          text2: response.message || 'Tidak dapat membuat QR code',
        });
      }
    } catch (error: any) {
      console.error('QR generation error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Generate QR Code',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsLoading(false);
    }
  }, [profileId]);

  useEffect(() => {
    if (visible && profileId) {
      fetchQRData();
    }
  }, [visible, profileId, fetchQRData]);







  const handleDownloadQR = async () => {
    try {
      if (!qrData?.qr_code) return;

      // Convert base64 to blob and download
      const response = await fetch(qrData.qr_code);
      const blob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `QR-${profileName.replace(/\s+/g, '_')}-${new Date().toISOString().split('T')[0]}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      Toast.show({
        type: 'success',
        text1: 'QR Code Diunduh',
        text2: 'QR code berhasil diunduh',
      });
    } catch (error) {
      console.error('Download error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Mengunduh',
        text2: 'Tidak dapat mengunduh QR code',
      });
    }
  };

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <Typography variant="h3" weight="semibold">
            QR Code Generator
          </Typography>
          <Button
            title="Tutup"
            variant="ghost"
            onPress={onClose}
            style={styles.closeButton}
          />
        </View>

        <View style={styles.content}>
          {isLoading ? (
            <Card variant="outlined" style={styles.loadingCard}>
              <CardContent>
                <View style={styles.loadingContent}>
                  <Typography variant="body1" align="center">
                    Membuat QR Code...
                  </Typography>
                </View>
              </CardContent>
            </Card>
          ) : qrData ? (
            <>
              <Card variant="elevated" style={styles.qrCard}>
                <CardHeader>
                  <Heading3 align="center">{profileName}</Heading3>
                  <Typography variant="body2" align="center" color="muted">
                    QR Code Profil Kesehatan
                  </Typography>
                </CardHeader>
                <CardContent>
                  <View style={styles.qrContainer}>
                    <QRCode
                      value={qrData.qr_code}
                      size={200}
                      color={colors.text}
                      backgroundColor={colors.surface}
                    />
                  </View>
                  
                  <View style={styles.qrInfo}>
                    <Typography variant="caption" align="center" color="muted">
                      Kode: {qrData.qr_code}
                    </Typography>
                  </View>
                </CardContent>
              </Card>

              <Card variant="outlined" style={styles.profileCard}>
                <CardContent>
                  <Typography variant="h4" weight="semibold" style={styles.profileTitle}>
                    Informasi Profil
                  </Typography>
                  
                  <View style={styles.profileInfo}>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">Nama:</Typography>
                      <Typography variant="body2" weight="medium">
                        {qrData.profile?.nama || '-'}
                      </Typography>
                    </View>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">Usia:</Typography>
                      <Typography variant="body2" weight="medium">
                        {qrData.profile?.usia || '-'} tahun
                      </Typography>
                    </View>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">Alamat:</Typography>
                      <Typography variant="body2" weight="medium" numberOfLines={2}>
                        {qrData.profile?.alamat || '-'}
                      </Typography>
                    </View>
                    <View style={styles.infoRow}>
                      <Typography variant="body2" color="muted">No. Telepon:</Typography>
                      <Typography variant="body2" weight="medium">
                        {qrData.profile?.no_telepon || '-'}
                      </Typography>
                    </View>
                  </View>
                </CardContent>
              </Card>

              <View style={styles.actionButtons}>
                <Button
                  title="Download QR Code"
                  onPress={handleDownloadQR}
                  icon={<Ionicons name="download" size={20} color={colors.primaryForeground} />}
                  style={styles.actionButton}
                />
              </View>
            </>
          ) : (
            <Card variant="outlined" style={styles.errorCard}>
              <CardContent>
                <View style={styles.errorContent}>
                  <Ionicons name="alert-circle" size={48} color={colors.error} />
                  <Typography variant="h4" align="center" style={styles.errorTitle}>
                    Gagal Membuat QR Code
                  </Typography>
                  <Typography variant="body2" align="center" color="muted">
                    Tidak dapat membuat QR code untuk profil ini
                  </Typography>
                  <Button
                    title="Coba Lagi"
                    onPress={fetchQRData}
                    style={styles.retryButton}
                  />
                </View>
              </CardContent>
            </Card>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closeButton: {
    minWidth: 60,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingCard: {
    marginBottom: 20,
  },
  loadingContent: {
    padding: 40,
  },
  qrCard: {
    marginBottom: 20,
  },
  qrContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  qrInfo: {
    marginTop: 16,
  },
  profileCard: {
    marginBottom: 20,
  },
  profileTitle: {
    marginBottom: 16,
  },
  profileInfo: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 4,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  errorCard: {
    flex: 1,
    justifyContent: 'center',
  },
  errorContent: {
    alignItems: 'center',
    gap: 16,
    padding: 20,
  },
  errorTitle: {
    marginTop: 8,
  },
  retryButton: {
    marginTop: 16,
  },
});
