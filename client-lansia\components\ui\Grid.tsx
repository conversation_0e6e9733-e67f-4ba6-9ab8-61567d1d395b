import React from 'react';
import {
  View,
  ViewStyle,
  Dimensions,
} from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface GridProps {
  children: React.ReactNode;
  columns?: number;
  spacing?: number;
  style?: ViewStyle;
}

interface GridItemProps {
  children: React.ReactNode;
  span?: number;
  style?: ViewStyle;
  _columns?: number;
  _spacing?: number;
  _index?: number;
}

// Main Grid Container
export const Grid: React.FC<GridProps> = ({
  children,
  columns = 2,
  spacing = 16,
  style,
}) => {
  const gridStyle: ViewStyle = {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing / 2,
  };

  const childrenWithProps = React.Children.map(children, (child, index) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as React.ReactElement<GridItemProps>, {
        ...(child.props as GridItemProps),
        _columns: columns,
        _spacing: spacing,
        _index: index,
      });
    }
    return child;
  });

  return (
    <View style={[gridStyle, style]}>
      {childrenWithProps}
    </View>
  );
};

// Grid Item Component
export const GridItem: React.FC<GridItemProps & {
  _columns?: number;
  _spacing?: number;
  _index?: number;
}> = ({
  children,
  span = 1,
  style,
  _columns = 2,
  _spacing = 16,
}) => {
  const itemWidth = (screenWidth - (_columns + 1) * _spacing) / _columns * span + (span - 1) * _spacing;
  
  const itemStyle: ViewStyle = {
    width: itemWidth,
    marginHorizontal: _spacing / 2,
    marginBottom: _spacing,
  };

  return (
    <View style={[itemStyle, style]}>
      {children}
    </View>
  );
};

// Responsive Grid Hook
export const useResponsiveGrid = () => {
  const getColumns = () => {
    if (screenWidth < 768) return 1; // Mobile
    if (screenWidth < 1024) return 2; // Tablet
    return 3; // Desktop
  };

  const getSpacing = () => {
    if (screenWidth < 768) return 12; // Mobile
    if (screenWidth < 1024) return 16; // Tablet
    return 20; // Desktop
  };

  return {
    columns: getColumns(),
    spacing: getSpacing(),
    isMobile: screenWidth < 768,
    isTablet: screenWidth >= 768 && screenWidth < 1024,
    isDesktop: screenWidth >= 1024,
  };
};

// Stats Grid Component (specialized for dashboard)
interface StatsGridProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const StatsGrid: React.FC<StatsGridProps> = ({ children, style }) => {
  const { columns, spacing } = useResponsiveGrid();
  
  return (
    <Grid columns={columns} spacing={spacing} style={style}>
      {children}
    </Grid>
  );
};

// Auto Grid Component (automatically adjusts based on content)
interface AutoGridProps {
  children: React.ReactNode;
  minItemWidth?: number;
  spacing?: number;
  style?: ViewStyle;
}

export const AutoGrid: React.FC<AutoGridProps> = ({
  children,
  minItemWidth = 150,
  spacing = 16,
  style,
}) => {
  const calculateColumns = () => {
    const availableWidth = screenWidth - spacing * 2;
    const itemsPerRow = Math.floor(availableWidth / (minItemWidth + spacing));
    return Math.max(1, itemsPerRow);
  };

  const columns = calculateColumns();

  return (
    <Grid columns={columns} spacing={spacing} style={style}>
      {children}
    </Grid>
  );
};

// Masonry Grid Component (for different height items)
interface MasonryGridProps {
  children: React.ReactNode;
  columns?: number;
  spacing?: number;
  style?: ViewStyle;
}

export const MasonryGrid: React.FC<MasonryGridProps> = ({
  children,
  columns = 2,
  spacing = 16,
  style,
}) => {
  const columnWrapperStyle: ViewStyle = {
    flex: 1,
    marginHorizontal: spacing / 2,
  };

  const createColumns = () => {
    const cols: React.ReactNode[][] = Array.from({ length: columns }, () => []);
    
    React.Children.forEach(children, (child, index) => {
      const columnIndex = index % columns;
      cols[columnIndex].push(
        <View key={index} style={{ marginBottom: spacing }}>
          {child}
        </View>
      );
    });

    return cols;
  };

  const columnData = createColumns();

  return (
    <View style={[{ flexDirection: 'row', marginHorizontal: -spacing / 2 }, style]}>
      {columnData.map((column, index) => (
        <View key={index} style={columnWrapperStyle}>
          {column}
        </View>
      ))}
    </View>
  );
};


