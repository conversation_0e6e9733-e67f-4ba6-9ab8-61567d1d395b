import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Button,
  Input,
  Card,
  CardContent,
  Typography,
  Heading2,
} from '@/components/ui';

// Validation schema
const loginSchema = yup.object().shape({
  username: yup
    .string()
    .required('Username wajib diisi')
    .min(3, 'Username minimal 3 karakter'),
  password: yup
    .string()
    .required('Password wajib diisi')
    .min(6, 'Password minimal 6 karakter'),
});

interface LoginFormData {
  username: string;
  password: string;
}

export default function LoginScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [isLoading, setIsLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      const response = await apiHelpers.login(data);
      
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Login Berhasil',
          text2: `Selamat datang, ${response.data.user.nama}!`,
        });
        
        // Navigate to main app
        router.replace('/(tabs)');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Login Gagal',
          text2: response.message || 'Username atau password salah',
        });
      }
    } catch (error: any) {
      console.error('Login error:', error);
      
      let errorMessage = 'Terjadi kesalahan saat login';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Toast.show({
        type: 'error',
        text1: 'Login Gagal',
        text2: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Lupa Password',
      'Silakan hubungi administrator untuk reset password.',
      [{ text: 'OK' }]
    );
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Typography variant="h1" align="center" color="primary" weight="bold">
            Aplikasi Kesehatan Lansia
          </Typography>
          <Typography variant="body1" align="center" color="muted" style={styles.subtitle}>
            Sistem Manajemen Kesehatan Posyandu
          </Typography>
        </View>

        <Card variant="elevated" style={styles.loginCard}>
          <CardContent>
            <Heading2 align="center" style={styles.loginTitle}>
              Masuk ke Aplikasi
            </Heading2>

            <View style={styles.form}>
              <Controller
                control={control}
                name="username"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Username"
                    placeholder="Masukkan username"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={errors.username?.message}
                    leftIcon="person"
                    autoCapitalize="none"
                    autoCorrect={false}
                    required
                    containerStyle={styles.inputContainer}
                  />
                )}
              />

              <Controller
                control={control}
                name="password"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Password"
                    placeholder="Masukkan password"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={errors.password?.message}
                    leftIcon="lock-closed"
                    secureTextEntry
                    required
                    containerStyle={styles.inputContainer}
                  />
                )}
              />

              <Button
                title="Masuk"
                onPress={handleSubmit(onSubmit)}
                loading={isLoading}
                style={styles.loginButton}
              />

              <Button
                title="Lupa Password?"
                variant="ghost"
                onPress={handleForgotPassword}
                style={styles.forgotButton}
              />
            </View>
          </CardContent>
        </Card>

        <View style={styles.footer}>
          <Typography variant="caption" align="center" color="muted">
            © 2024 Aplikasi Kesehatan Lansia
          </Typography>
          <Typography variant="caption" align="center" color="muted">
            Untuk Posyandu dan Puskesmas
          </Typography>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    marginBottom: 40,
  },
  subtitle: {
    marginTop: 8,
  },
  loginCard: {
    marginBottom: 30,
  },
  loginTitle: {
    marginBottom: 30,
  },
  form: {
    gap: 20,
  },
  inputContainer: {
    marginBottom: 4,
  },
  loginButton: {
    marginTop: 10,
  },
  forgotButton: {
    marginTop: 10,
  },
  footer: {
    marginTop: 20,
    gap: 4,
  },
});
