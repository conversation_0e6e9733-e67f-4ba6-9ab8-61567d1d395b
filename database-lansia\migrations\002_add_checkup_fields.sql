-- =====================================================
-- Migration: Add additional fields to checkups table
-- Date: 2025-08-02
-- Description: Add weight, height, and complaints fields
-- =====================================================

USE app_lansia;

-- Add new columns to checkups table
ALTER TABLE checkups 
ADD COLUMN berat_badan DECIMAL(5,2) NULL AFTER gula_darah,
ADD COLUMN tinggi_badan DECIMAL(5,2) NULL AFTER berat_badan,
ADD COLUMN keluhan TEXT NULL AFTER tinggi_badan;

-- Add indexes for the new fields
ALTER TABLE checkups 
ADD INDEX idx_berat_badan (berat_badan),
ADD INDEX idx_tinggi_badan (tinggi_badan);

-- Update existing checkups with sample data (optional)
-- This is just for testing purposes
UPDATE checkups SET 
    berat_badan = 65.0,
    tinggi_badan = 160.0,
    keluhan = 'Tidak ada keluhan khusus'
WHERE id = 1;

UPDATE checkups SET 
    berat_badan = 70.0,
    tinggi_badan = 165.0,
    keluhan = 'Sedikit pusing'
WHERE id = 2;
