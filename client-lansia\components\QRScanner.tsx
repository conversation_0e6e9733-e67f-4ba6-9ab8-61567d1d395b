import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Modal,
  TouchableOpacity,
} from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Button,
  Card,
  CardContent,
  Typography,
  Input,
} from '@/components/ui';



interface QRScannerProps {
  visible: boolean;
  onClose: () => void;
  onScanSuccess: (profileData: any) => void;
}

export const QRScanner: React.FC<QRScannerProps> = ({
  visible,
  onClose,
  onScanSuccess,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [showManualInput, setShowManualInput] = useState(false);
  const [manualCode, setManualCode] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (visible) {
      setScanned(false);
      setShowManualInput(false);
      setManualCode('');
    }
  }, [visible]);

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (scanned || isProcessing) return;
    
    setScanned(true);
    await processQRCode(data);
  };

  const processQRCode = async (qrData: string) => {
    setIsProcessing(true);
    try {
      const response = await apiHelpers.scanQRCode(qrData);
      
      if (response.success && response.data) {
        Toast.show({
          type: 'success',
          text1: 'QR Code Berhasil Dipindai',
          text2: `Profil ${response.data.nama} ditemukan`,
        });
        
        onScanSuccess(response.data);
        onClose();
      } else {
        Toast.show({
          type: 'error',
          text1: 'QR Code Tidak Valid',
          text2: response.message || 'Data tidak ditemukan',
        });
        
        // Allow scanning again after error
        setTimeout(() => setScanned(false), 2000);
      }
    } catch (error: any) {
      console.error('QR scan error:', error);
      
      Toast.show({
        type: 'error',
        text1: 'Gagal Memproses QR Code',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
      
      // Allow scanning again after error
      setTimeout(() => setScanned(false), 2000);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleManualSubmit = async () => {
    if (!manualCode.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Kode QR Kosong',
        text2: 'Silakan masukkan kode QR',
      });
      return;
    }

    await processQRCode(manualCode.trim());
  };

  const requestCameraPermission = async () => {
    const { granted } = await requestPermission();
    if (!granted) {
      Alert.alert(
        'Izin Kamera Diperlukan',
        'Aplikasi memerlukan akses kamera untuk memindai QR code. Anda dapat memasukkan kode secara manual sebagai alternatif.',
        [
          { text: 'Input Manual', onPress: () => setShowManualInput(true) },
          { text: 'Batal', onPress: onClose },
        ]
      );
    }
  };

  if (!permission) {
    return null;
  }

  if (!permission.granted) {
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.container, { backgroundColor: colors.background }]}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Typography variant="h3" weight="semibold">
              Pindai QR Code
            </Typography>
          </View>

          <View style={styles.content}>
            <Card variant="outlined" style={styles.permissionCard}>
              <CardContent>
                <View style={styles.permissionContent}>
                  <Ionicons name="camera" size={64} color={colors.textMuted} />
                  <Typography variant="h4" align="center" style={styles.permissionTitle}>
                    Izin Kamera Diperlukan
                  </Typography>
                  <Typography variant="body2" align="center" color="muted">
                    Aplikasi memerlukan akses kamera untuk memindai QR code profil lansia
                  </Typography>
                </View>
              </CardContent>
            </Card>

            <View style={styles.buttonContainer}>
              <Button
                title="Berikan Izin Kamera"
                onPress={requestCameraPermission}
                style={styles.button}
              />
              <Button
                title="Input Manual"
                variant="outline"
                onPress={() => setShowManualInput(true)}
                style={styles.button}
              />
            </View>
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="fullScreen">
      <View style={styles.scannerContainer}>
        {!showManualInput ? (
          <>
            <CameraView
              style={styles.camera}
              facing="back"
              onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
              barcodeScannerSettings={{
                barcodeTypes: ['qr'],
              }}
            />
            
            <View style={styles.overlay}>
              <View style={styles.scannerHeader}>
                <TouchableOpacity onPress={onClose} style={styles.headerButton}>
                  <Ionicons name="close" size={24} color="white" />
                </TouchableOpacity>
                <Typography variant="h4" style={{ color: 'white' }} weight="semibold">
                  Pindai QR Code
                </Typography>
                <TouchableOpacity 
                  onPress={() => setShowManualInput(true)} 
                  style={styles.headerButton}
                >
                  <Ionicons name="keypad" size={24} color="white" />
                </TouchableOpacity>
              </View>

              <View style={styles.scannerFrame}>
                <View style={styles.scannerCorner} />
                <View style={[styles.scannerCorner, styles.topRight]} />
                <View style={[styles.scannerCorner, styles.bottomLeft]} />
                <View style={[styles.scannerCorner, styles.bottomRight]} />
              </View>

              <View style={styles.scannerFooter}>
                <Typography variant="body1" align="center" style={{ color: 'white' }}>
                  Arahkan kamera ke QR code profil lansia
                </Typography>
                {scanned && (
                  <Typography variant="body2" align="center" style={{ color: 'white', marginTop: 8 }}>
                    {isProcessing ? 'Memproses...' : 'QR code terdeteksi'}
                  </Typography>
                )}
              </View>
            </View>
          </>
        ) : (
          <View style={[styles.manualInputContainer, { backgroundColor: colors.background }]}>
            <View style={styles.header}>
              <TouchableOpacity onPress={() => setShowManualInput(false)} style={styles.closeButton}>
                <Ionicons name="arrow-back" size={24} color={colors.text} />
              </TouchableOpacity>
              <Typography variant="h3" weight="semibold">
                Input Manual QR Code
              </Typography>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.content}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="body1" color="muted" style={styles.manualDescription}>
                    Masukkan kode QR secara manual jika tidak dapat memindai dengan kamera
                  </Typography>
                  
                  <Input
                    label="Kode QR"
                    placeholder="Masukkan kode QR"
                    value={manualCode}
                    onChangeText={setManualCode}
                    multiline
                    numberOfLines={4}
                    containerStyle={styles.manualInput}
                  />
                  
                  <Button
                    title="Proses QR Code"
                    onPress={handleManualSubmit}
                    loading={isProcessing}
                    style={styles.submitButton}
                  />
                </CardContent>
              </Card>
            </View>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scannerContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  scannerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerFrame: {
    alignSelf: 'center',
    width: 250,
    height: 250,
    position: 'relative',
  },
  scannerCorner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: 'white',
    top: 0,
    left: 0,
    borderTopWidth: 3,
    borderLeftWidth: 3,
  },
  topRight: {
    top: 0,
    right: 0,
    left: 'auto',
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderLeftWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    top: 'auto',
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    top: 'auto',
    left: 'auto',
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderTopWidth: 0,
    borderLeftWidth: 0,
  },
  scannerFooter: {
    paddingHorizontal: 20,
    paddingBottom: 60,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closeButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  permissionCard: {
    marginBottom: 30,
  },
  permissionContent: {
    alignItems: 'center',
    gap: 16,
  },
  permissionTitle: {
    marginTop: 8,
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    marginBottom: 8,
  },
  manualInputContainer: {
    flex: 1,
  },
  manualDescription: {
    marginBottom: 20,
  },
  manualInput: {
    marginBottom: 20,
  },
  submitButton: {
    marginTop: 10,
  },
});
