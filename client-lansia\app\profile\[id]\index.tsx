import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading2,
  Heading3,
  But<PERSON>,
  StatusBadge,
} from '@/components/ui';
import { QRGenerator } from '@/components/QRGenerator';

interface ProfileData {
  id: string;
  nama: string;
  usia: number;
  alamat: string;
  no_telepon: string;
  kontak_darurat: string;
  riwayat_penyakit?: string;
  obat_rutin?: string;
  alergi?: string;
  created_at: string;
}

interface CheckupRecord {
  id: string;
  gula_darah: number;
  tekanan_darah: string;
  berat_badan?: number;
  tinggi_badan?: number;
  tanggal: string;
  catatan?: string;
}

interface ProfileDetailData {
  profile: ProfileData;
  checkups: CheckupRecord[];
  statistics: {
    total_checkups: number;
    avg_gula_darah: number;
    first_checkup?: string;
    last_checkup?: string;
  };
}

export default function ProfileDetailScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { id } = useLocalSearchParams<{ id: string }>();
  const [profileData, setProfileData] = useState<ProfileDetailData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showQRGenerator, setShowQRGenerator] = useState(false);

  const loadProfileData = useCallback(async (isRefresh = false) => {
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const response = await apiHelpers.getProfileById(id);

      if (response.success && response.data) {
        setProfileData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Memuat Profil',
          text2: response.message || 'Profil tidak ditemukan',
        });
      }
    } catch (error: any) {
      console.error('Profile load error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Memuat Profil',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      loadProfileData();
    }
  }, [id, loadProfileData]);

  const handleRefresh = () => {
    loadProfileData(true);
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const getHealthStatus = (): 'good' | 'warning' | 'danger' | 'neutral' => {
    if (!profileData?.statistics.avg_gula_darah) return 'neutral';
    const avgSugar = profileData.statistics.avg_gula_darah;
    if (avgSugar < 70 || avgSugar > 200) return 'danger';
    if (avgSugar < 80 || avgSugar > 140) return 'warning';
    return 'good';
  };

  const getHealthStatusLabel = (status: string): string => {
    switch (status) {
      case 'good': return 'Sehat';
      case 'warning': return 'Perhatian';
      case 'danger': return 'Risiko Tinggi';
      default: return 'Belum Diperiksa';
    }
  };

  if (isLoading && !profileData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Typography variant="body1" align="center">
            Memuat data profil...
          </Typography>
        </View>
      </View>
    );
  }

  if (!profileData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <Typography variant="body1" align="center" color="muted">
            Profil tidak ditemukan
          </Typography>
          <Button
            title="Kembali"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header with Back Button */}
      <View style={[styles.headerContainer, { backgroundColor: colors.background }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background, borderWidth: 1, borderColor: 'rgba(0, 0, 0, 0.1)' }]}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerTitle}>
          <Typography variant="h4" weight="semibold">
            Detail Profil
          </Typography>
        </View>
        <TouchableOpacity
          style={[styles.menuButton, { backgroundColor: colors.background, borderWidth: 1, borderColor: 'rgba(0, 0, 0, 0.1)' }]}
          onPress={() => setShowQRGenerator(true)}
        >
          <Ionicons name="qr-code" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Profile Info */}
        <Card variant="elevated" style={styles.profileCard}>
          <CardContent>
            <View style={styles.profileHeader}>
              <View style={styles.profileInfo}>
                <Heading2 weight="bold">{profileData.profile.nama}</Heading2>
                <Typography variant="body1" color="muted">
                  {profileData.profile.usia} tahun
                </Typography>
                <Typography variant="body2" color="muted">
                  Terdaftar: {formatDate(profileData.profile.created_at)}
                </Typography>
              </View>
              <StatusBadge
                status={getHealthStatus()}
                label={getHealthStatusLabel(getHealthStatus())}
              />
            </View>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card variant="outlined" style={styles.infoCard}>
          <CardHeader>
            <Heading3>Informasi Kontak</Heading3>
          </CardHeader>
          <CardContent>
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Ionicons name="location" size={20} color={colors.textMuted} />
                <Typography variant="body2">{profileData.profile.alamat}</Typography>
              </View>
              <View style={styles.contactItem}>
                <Ionicons name="call" size={20} color={colors.textMuted} />
                <Typography variant="body2">{profileData.profile.no_telepon}</Typography>
              </View>
              <View style={styles.contactItem}>
                <Ionicons name="call" size={20} color={colors.textMuted} />
                <Typography variant="body2">
                  Darurat: {profileData.profile.kontak_darurat}
                </Typography>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Health Statistics */}
        {profileData.statistics && (
          <Card variant="outlined" style={styles.statsCard}>
            <CardHeader>
              <Heading3>Ringkasan Kesehatan</Heading3>
            </CardHeader>
            <CardContent>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {profileData.statistics.total_checkups}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Total Pemeriksaan
                  </Typography>
                </View>
                
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {profileData.statistics.avg_gula_darah?.toFixed(0) || 0}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Rata-rata Gula Darah
                  </Typography>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card variant="outlined" style={styles.actionsCard}>
          <CardHeader>
            <Heading3>Aksi Cepat</Heading3>
          </CardHeader>
          <CardContent>
            <View style={styles.actionButtons}>
              <Button
                title="Tambah Pemeriksaan"
                onPress={() => router.push(`/checkup/add?profileId=${id}`)}
                icon={<Ionicons name="add" size={20} color={colors.primaryForeground} />}
                style={styles.actionButton}
              />
              <Button
                title="Lihat Riwayat"
                variant="outline"
                onPress={() => router.push(`/profile/${id}/history`)}
                icon={<Ionicons name="time" size={20} color={colors.primary} />}
                style={styles.actionButton}
              />
              <Button
                title="Lihat Grafik"
                variant="outline"
                onPress={() => router.push(`/profile/${id}/charts`)}
                icon={<Ionicons name="analytics" size={20} color={colors.primary} />}
                style={styles.actionButton}
              />
            </View>
          </CardContent>
        </Card>
      </ScrollView>

      {/* QR Generator Modal */}
      <QRGenerator
        visible={showQRGenerator}
        onClose={() => setShowQRGenerator(false)}
        profileId={id}
        profileName={profileData.profile.nama}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerTitle: {
    flex: 1,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  profileCard: {
    marginBottom: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  profileInfo: {
    flex: 1,
    gap: 4,
  },
  infoCard: {
    marginBottom: 20,
  },
  contactInfo: {
    gap: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statsCard: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
  },
  actionsCard: {
    marginBottom: 20,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
});
