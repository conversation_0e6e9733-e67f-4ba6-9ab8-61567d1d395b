-- =====================================================
-- Database Schema for Aplikasi Kesehatan Lansia
-- =====================================================

-- Create database
CREATE DATABASE IF NOT EXISTS app_lansia;
USE app_lansia;

-- =====================================================
-- Table: users (Admin Authentication)
-- =====================================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'kader') DEFAULT 'kader',
    posyandu_name VARCHAR(100) NOT NULL,
    pin VARCHAR(6),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active)
);

-- =====================================================
-- Table: profiles (Elderly Profiles)
-- =====================================================
CREATE TABLE profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nama VARCHAR(100) NOT NULL,
    usia INT NOT NULL CHECK (usia >= 45 AND usia <= 120),
    alamat TEXT NOT NULL,
    riwayat_medis TEXT,
    qr_code VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nama (nama),
    INDEX idx_usia (usia),
    INDEX idx_qr_code (qr_code),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- Table: checkups (Health Examinations)
-- =====================================================
CREATE TABLE checkups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    profile_id INT NOT NULL,
    tekanan_darah VARCHAR(20) NOT NULL,
    gula_darah INT NOT NULL CHECK (gula_darah >= 50 AND gula_darah <= 500),
    tanggal DATE NOT NULL,
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE,
    
    INDEX idx_profile_id (profile_id),
    INDEX idx_tanggal (tanggal),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- Insert Default Admin User
-- =====================================================
INSERT INTO users (username, password, role, posyandu_name, pin) VALUES 
('admin', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', 'admin', 'Posyandu Sehat Bersama', '123456');

-- =====================================================
-- Sample Data for Testing
-- =====================================================
INSERT INTO profiles (nama, usia, alamat, riwayat_medis, qr_code) VALUES 
('Siti Aminah', 65, 'Jl. Merdeka No. 123, Jakarta', 'Hipertensi, Diabetes', 'QR001'),
('Budi Santoso', 70, 'Jl. Sudirman No. 456, Jakarta', 'Kolesterol tinggi', 'QR002'),
('Ratna Sari', 68, 'Jl. Thamrin No. 789, Jakarta', 'Asam urat', 'QR003');

INSERT INTO checkups (profile_id, tekanan_darah, gula_darah, tanggal, catatan) VALUES 
(1, '140/90', 180, '2024-01-15', 'Tekanan darah tinggi, perlu kontrol rutin'),
(1, '135/85', 160, '2024-01-22', 'Sedikit membaik'),
(2, '130/80', 120, '2024-01-16', 'Kondisi stabil'),
(3, '125/75', 110, '2024-01-17', 'Kondisi baik');

-- =====================================================
-- Views for Easy Data Access
-- =====================================================
CREATE VIEW profile_with_latest_checkup AS
SELECT 
    p.*,
    c.tekanan_darah as latest_tekanan_darah,
    c.gula_darah as latest_gula_darah,
    c.tanggal as latest_checkup_date,
    c.catatan as latest_catatan
FROM profiles p
LEFT JOIN checkups c ON p.id = c.profile_id
WHERE c.id = (
    SELECT MAX(id) FROM checkups WHERE profile_id = p.id
);

CREATE VIEW checkup_statistics AS
SELECT 
    p.id,
    p.nama,
    COUNT(c.id) as total_checkups,
    AVG(c.gula_darah) as avg_gula_darah,
    MIN(c.tanggal) as first_checkup,
    MAX(c.tanggal) as last_checkup
FROM profiles p
LEFT JOIN checkups c ON p.id = c.profile_id
GROUP BY p.id, p.nama;

-- =====================================================
-- Stored Procedures
-- =====================================================
DELIMITER //

CREATE PROCEDURE GetProfileWithHistory(IN profile_id INT)
BEGIN
    SELECT * FROM profiles WHERE id = profile_id;
    SELECT * FROM checkups WHERE profile_id = profile_id ORDER BY tanggal DESC;
END //

CREATE PROCEDURE AddCheckupWithValidation(
    IN p_profile_id INT,
    IN p_tekanan_darah VARCHAR(20),
    IN p_gula_darah INT,
    IN p_tanggal DATE,
    IN p_catatan TEXT
)
BEGIN
    DECLARE profile_exists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO profile_exists FROM profiles WHERE id = p_profile_id;
    
    IF profile_exists > 0 THEN
        INSERT INTO checkups (profile_id, tekanan_darah, gula_darah, tanggal, catatan)
        VALUES (p_profile_id, p_tekanan_darah, p_gula_darah, p_tanggal, p_catatan);
        
        UPDATE profiles SET updated_at = CURRENT_TIMESTAMP WHERE id = p_profile_id;
        
        SELECT 'SUCCESS' as status, LAST_INSERT_ID() as checkup_id;
    ELSE
        SELECT 'ERROR' as status, 'Profile not found' as message;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- Triggers for Data Integrity
-- =====================================================
DELIMITER //

CREATE TRIGGER before_profile_insert
BEFORE INSERT ON profiles
FOR EACH ROW
BEGIN
    IF NEW.qr_code IS NULL THEN
        SET NEW.qr_code = CONCAT('QR', LPAD(NEW.id, 6, '0'));
    END IF;
END //

CREATE TRIGGER after_checkup_insert
AFTER INSERT ON checkups
FOR EACH ROW
BEGIN
    UPDATE profiles 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.profile_id;
END //

DELIMITER ;
