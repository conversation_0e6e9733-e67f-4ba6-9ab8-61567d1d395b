import React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Typography } from './ui';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
}

export function LoadingSpinner({ 
  size = 'large', 
  color, 
  message = 'Memuat...', 
  overlay = false 
}: LoadingSpinnerProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const spinnerColor = color || colors.primary;

  const content = (
    <View style={[
      styles.container,
      overlay && styles.overlay,
      { backgroundColor: overlay ? 'rgba(0, 0, 0, 0.5)' : 'transparent' }
    ]}>
      <View style={[
        styles.spinner,
        { backgroundColor: overlay ? colors.surface : 'transparent' }
      ]}>
        <ActivityIndicator 
          size={size} 
          color={spinnerColor} 
          style={styles.indicator}
        />
        {message && (
          <Typography 
            variant="body2" 
            color={overlay ? undefined : 'muted'}
            align="center"
            style={styles.message}
          >
            {message}
          </Typography>
        )}
      </View>
    </View>
  );

  return content;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 100,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  spinner: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    gap: 12,
  },
  indicator: {
    marginBottom: 4,
  },
  message: {
    marginTop: 8,
  },
});
