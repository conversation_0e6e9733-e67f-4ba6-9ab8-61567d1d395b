import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { apiHelpers } from '@/config/api';
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Heading3,
  Button,
  StatusBadge,
} from '@/components/ui';
import { QRScanner } from '@/components/QRScanner';
import { QRGenerator } from '@/components/QRGenerator';

interface ProfileItem {
  id: string;
  nama: string;
  usia: number;
  alamat: string;
  no_telepon: string;
  kontak_darurat: string;
  created_at: string;
  last_checkup?: string;
  total_checkups: number;
  avg_gula_darah?: number;
  status_kesehatan: 'good' | 'warning' | 'danger' | 'neutral';
}

interface ProfilesData {
  profiles: ProfileItem[];
  total: number;
  stats: {
    totalProfiles: number;
    activeProfiles: number;
    recentCheckups: number;
  };
}

export default function ProfilesScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [profilesData, setProfilesData] = useState<ProfilesData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showQRGenerator, setShowQRGenerator] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<{ id: string; nama: string } | null>(null);

  useEffect(() => {
    loadProfilesData();
  }, []);

  const loadProfilesData = async (isRefresh = false, search = '') => {
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const response = await apiHelpers.getProfiles({ search });
      
      if (response.success && response.data) {
        setProfilesData(response.data);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Gagal Memuat Profil',
          text2: response.message || 'Tidak dapat memuat daftar profil',
        });
      }
    } catch (error: any) {
      console.error('Profiles load error:', error);
      Toast.show({
        type: 'error',
        text1: 'Gagal Memuat Profil',
        text2: error.response?.data?.message || 'Terjadi kesalahan',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadProfilesData(true, searchQuery);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // Debounce search
    const timeoutId = setTimeout(() => {
      loadProfilesData(false, query);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  };

  const handleQRScanSuccess = (profileData: any) => {
    Toast.show({
      type: 'success',
      text1: 'Profil Ditemukan',
      text2: `${profileData.nama} - ${profileData.usia} tahun`,
    });
    
    router.push(`/profile/${profileData.id}` as any);
  };

  const handleGenerateQR = (profile: ProfileItem) => {
    setSelectedProfile({ id: profile.id, nama: profile.nama });
    setShowQRGenerator(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const getHealthStatusLabel = (status: string): string => {
    switch (status) {
      case 'good': return 'Sehat';
      case 'warning': return 'Perhatian';
      case 'danger': return 'Risiko Tinggi';
      default: return 'Belum Diperiksa';
    }
  };

  if (isLoading && !profilesData) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <Typography variant="body1" align="center">
            Memuat daftar profil...
          </Typography>
        </View>
      </View>
    );
  }

  const filteredProfiles = profilesData?.profiles || [];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header with Back Button */}
      <View style={[styles.headerContainer, { backgroundColor: colors.background }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.background, borderWidth: 1, borderColor: 'rgba(0, 0, 0, 0.1)' }]}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerTitle}>
          <Typography variant="h4" weight="semibold">
            Daftar Profil Lansia
          </Typography>
          <Typography variant="body2" color="muted">
            Kelola profil dan riwayat kesehatan
          </Typography>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >

        {/* Search and Actions */}
        <Card variant="outlined" style={styles.searchCard}>
          <CardContent>
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <Ionicons name="search" size={20} color={colors.textMuted} style={styles.searchIcon} />
                <TextInput
                  style={[styles.searchInput, { color: colors.text }]}
                  placeholder="Cari nama atau alamat..."
                  placeholderTextColor={colors.textMuted}
                  value={searchQuery}
                  onChangeText={handleSearch}
                />
              </View>
              <TouchableOpacity
                style={[styles.qrButton, { backgroundColor: colors.primary }]}
                onPress={() => setShowQRScanner(true)}
              >
                <Ionicons name="qr-code" size={20} color={colors.primaryForeground} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.actionButtons}>
              <Button
                title="Tambah Profil Baru"
                onPress={() => router.push('/profile/add')}
                icon={<Ionicons name="person-add" size={20} color={colors.primaryForeground} />}
                style={styles.addButton}
              />
            </View>
          </CardContent>
        </Card>

        {/* Statistics */}
        {profilesData?.stats && (
          <Card variant="elevated" style={styles.statsCard}>
            <CardHeader>
              <Heading3>Ringkasan</Heading3>
            </CardHeader>
            <CardContent>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {profilesData.stats.totalProfiles}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Total Profil
                  </Typography>
                </View>
                
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {profilesData.stats.activeProfiles}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Aktif Bulan Ini
                  </Typography>
                </View>
                
                <View style={styles.statItem}>
                  <Typography variant="h3" weight="bold">
                    {profilesData.stats.recentCheckups}
                  </Typography>
                  <Typography variant="caption" color="muted">
                    Pemeriksaan Minggu Ini
                  </Typography>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Profiles List */}
        <View style={styles.profilesSection}>
          <Typography variant="h4" weight="semibold" style={styles.sectionTitle}>
            Profil Lansia ({filteredProfiles.length})
          </Typography>
          
          {filteredProfiles.length === 0 ? (
            <Card variant="outlined" style={styles.emptyCard}>
              <CardContent>
                <View style={styles.emptyContent}>
                  <Ionicons name="people" size={48} color={colors.textMuted} />
                  <Typography variant="body1" align="center" color="muted">
                    {searchQuery ? 'Tidak ada profil yang cocok' : 'Belum ada profil lansia'}
                  </Typography>
                  <Typography variant="body2" align="center" color="muted">
                    {searchQuery ? 'Coba kata kunci lain' : 'Tambahkan profil pertama untuk memulai'}
                  </Typography>
                  {!searchQuery && (
                    <Button
                      title="Tambah Profil"
                      onPress={() => router.push('/profile/add')}
                      style={styles.emptyButton}
                    />
                  )}
                </View>
              </CardContent>
            </Card>
          ) : (
            <View style={styles.profilesList}>
              {filteredProfiles.map((profile) => (
                <Card key={profile.id} variant="outlined" style={styles.profileCard}>
                  <CardContent>
                    <TouchableOpacity
                      onPress={() => router.push(`/profile/${profile.id}` as any)}
                      style={styles.profileContent}
                    >
                      <View style={styles.profileHeader}>
                        <View style={styles.profileInfo}>
                          <Typography variant="h4" weight="semibold">
                            {profile.nama}
                          </Typography>
                          <Typography variant="body2" color="muted">
                            {profile.usia} tahun • {profile.alamat}
                          </Typography>
                        </View>
                        
                        <View style={styles.profileStatus}>
                          <StatusBadge
                            status={profile.status_kesehatan}
                            label={getHealthStatusLabel(profile.status_kesehatan)}
                            size="sm"
                          />
                        </View>
                      </View>
                      
                      <View style={styles.profileDetails}>
                        <View style={styles.detailItem}>
                          <Ionicons name="call" size={16} color={colors.textMuted} />
                          <Typography variant="body2" color="muted">
                            {profile.no_telepon}
                          </Typography>
                        </View>
                        
                        <View style={styles.detailItem}>
                          <Ionicons name="medical" size={16} color={colors.textMuted} />
                          <Typography variant="body2" color="muted">
                            {profile.total_checkups} pemeriksaan
                          </Typography>
                        </View>
                        
                        {profile.last_checkup && (
                          <View style={styles.detailItem}>
                            <Ionicons name="time" size={16} color={colors.textMuted} />
                            <Typography variant="body2" color="muted">
                              Terakhir: {formatDate(profile.last_checkup)}
                            </Typography>
                          </View>
                        )}
                        
                        {profile.avg_gula_darah && (
                          <View style={styles.detailItem}>
                            <Ionicons name="analytics" size={16} color={colors.textMuted} />
                            <Typography variant="body2" color="muted">
                              Rata-rata gula darah: {profile.avg_gula_darah.toFixed(0)} mg/dL
                            </Typography>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                    
                    <View style={styles.profileActions}>
                      <Button
                        title="QR Code"
                        variant="ghost"
                        onPress={() => handleGenerateQR(profile)}
                        icon={<Ionicons name="qr-code" size={16} color={colors.text} />}
                        style={styles.actionButton}
                      />
                      <Button
                        title="Pemeriksaan"
                        variant="outline"
                        onPress={() => router.push(`/checkup/add?profileId=${profile.id}`)}
                        icon={<Ionicons name="add" size={16} color={colors.primary} />}
                        style={styles.actionButton}
                      />
                      <Button
                        title="Riwayat"
                        variant="ghost"
                        onPress={() => router.push(`/profile/${profile.id}/history`)}
                        icon={<Ionicons name="time" size={16} color={colors.text} />}
                        style={styles.actionButton}
                      />
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* QR Scanner Modal */}
      <QRScanner
        visible={showQRScanner}
        onClose={() => setShowQRScanner(false)}
        onScanSuccess={handleQRScanSuccess}
      />

      {/* QR Generator Modal */}
      {selectedProfile && (
        <QRGenerator
          visible={showQRGenerator}
          onClose={() => {
            setShowQRGenerator(false);
            setSelectedProfile(null);
          }}
          profileId={selectedProfile.id}
          profileName={selectedProfile.nama}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    marginBottom: 24,
  },
  searchCard: {
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 16,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  qrButton: {
    width: 44,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtons: {
    gap: 8,
  },
  addButton: {
    marginBottom: 4,
  },
  statsCard: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
  },
  profilesSection: {
    gap: 16,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  emptyCard: {
    padding: 20,
  },
  emptyContent: {
    alignItems: 'center',
    gap: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  profilesList: {
    gap: 12,
  },
  profileCard: {
    marginBottom: 8,
  },
  profileContent: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  profileInfo: {
    flex: 1,
    gap: 4,
  },
  profileStatus: {
    marginLeft: 12,
  },
  profileDetails: {
    gap: 6,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  profileActions: {
    flexDirection: 'row',
    gap: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  actionButton: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerTitle: {
    flex: 1,
  },
});
