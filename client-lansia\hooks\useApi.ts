import { useState, useCallback } from 'react';
import Toast from 'react-native-toast-message';

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiOptions {
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
}

export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<any>,
  options: UseApiOptions = {}
) {
  const {
    showErrorToast = true,
    showSuccessToast = false,
    successMessage = 'Berhasil'
  } = options;

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async (...args: any[]) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiFunction(...args);
      
      if (response.success) {
        setState({
          data: response.data,
          loading: false,
          error: null,
        });

        if (showSuccessToast) {
          Toast.show({
            type: 'success',
            text1: 'Berhasil',
            text2: response.message || successMessage,
          });
        }

        return response;
      } else {
        const errorMessage = response.message || 'Terjadi kesalahan';
        setState({
          data: null,
          loading: false,
          error: errorMessage,
        });

        if (showErrorToast) {
          Toast.show({
            type: 'error',
            text1: 'Gagal',
            text2: errorMessage,
          });
        }

        return response;
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 
                          error.message || 
                          'Terjadi kesalahan jaringan';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
      });

      if (showErrorToast) {
        Toast.show({
          type: 'error',
          text1: 'Gagal',
          text2: errorMessage,
        });
      }

      throw error;
    }
  }, [apiFunction, showErrorToast, showSuccessToast, successMessage]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

// Specialized hooks for common operations
export function useApiMutation<T = any>(
  apiFunction: (...args: any[]) => Promise<any>,
  options: UseApiOptions = {}
) {
  return useApi<T>(apiFunction, {
    showErrorToast: true,
    showSuccessToast: true,
    ...options,
  });
}

export function useApiQuery<T = any>(
  apiFunction: (...args: any[]) => Promise<any>,
  options: UseApiOptions = {}
) {
  return useApi<T>(apiFunction, {
    showErrorToast: true,
    showSuccessToast: false,
    ...options,
  });
}

// Hook for handling form submissions
export function useFormSubmission<T = any>(
  apiFunction: (...args: any[]) => Promise<any>,
  options: UseApiOptions & {
    onSuccess?: (data: T) => void;
    onError?: (error: string) => void;
  } = {}
) {
  const { onSuccess, onError, ...apiOptions } = options;
  
  const api = useApiMutation<T>(apiFunction, apiOptions);

  const submit = useCallback(async (...args: any[]) => {
    try {
      const response = await api.execute(...args);
      if (response.success && onSuccess) {
        onSuccess(response.data);
      }
      return response;
    } catch (error: any) {
      if (onError) {
        onError(error.message || 'Terjadi kesalahan');
      }
      throw error;
    }
  }, [api, onSuccess, onError]);

  return {
    ...api,
    submit,
  };
}

// Hook for data fetching with automatic loading states
export function useDataFetcher<T = any>(
  apiFunction: (...args: any[]) => Promise<any>,
  dependencies: any[] = [],
  options: UseApiOptions = {}
) {
  const api = useApiQuery<T>(apiFunction, options);
  
  const fetch = useCallback((...args: any[]) => {
    return api.execute(...args);
  }, [api]);

  const refetch = useCallback(() => {
    if (dependencies.length > 0) {
      return api.execute(...dependencies);
    }
    return api.execute();
  }, [api, dependencies]);

  return {
    ...api,
    fetch,
    refetch,
  };
}

// Hook for handling paginated data
export function usePaginatedApi<T = any>(
  apiFunction: (params: { page: number; limit: number; [key: string]: any }) => Promise<any>,
  initialParams: { limit?: number; [key: string]: any } = {},
  options: UseApiOptions = {}
) {
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [allData, setAllData] = useState<T[]>([]);

  const api = useApiQuery(apiFunction, options);

  const loadPage = useCallback(async (pageNum: number, reset = false) => {
    try {
      const response = await api.execute({
        page: pageNum,
        limit: initialParams.limit || 10,
        ...initialParams,
      });

      if (response.success) {
        const newData = response.data.items || response.data || [];
        const total = response.data.total || 0;
        const currentTotal = reset ? newData.length : allData.length + newData.length;

        setAllData(prev => reset ? newData : [...prev, ...newData]);
        setHasMore(currentTotal < total);
        setPage(pageNum);
      }

      return response;
    } catch (error) {
      throw error;
    }
  }, [api, initialParams, allData.length]);

  const loadMore = useCallback(() => {
    if (!api.loading && hasMore) {
      return loadPage(page + 1);
    }
  }, [loadPage, page, hasMore, api.loading]);

  const refresh = useCallback(() => {
    setAllData([]);
    setPage(1);
    setHasMore(true);
    return loadPage(1, true);
  }, [loadPage]);

  return {
    data: allData,
    loading: api.loading,
    error: api.error,
    hasMore,
    page,
    loadMore,
    refresh,
    reset: api.reset,
  };
}
